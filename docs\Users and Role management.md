
# ProManage Role-Based Access Control System
## Complete Implementation Specification

> **Document Purpose**: This document provides a comprehensive, implementable specification for the ProManage permissions/role-based access control system. This system implements a 3-layer hierarchical permission model that integrates seamlessly with the existing ProManage architecture.

---

## 1. System Overview

### 1.1 Objective

Design and implement a centralized, hierarchical permission system that provides:

* **Form Visibility Control**: Determine which forms appear in the ribbon interface
* **Form Access Control**: Control user ability to open and view forms
* **Action-Level Permissions**: Granular control over New, Edit, and Delete operations
* **Global Override Capability**: UserManagement form permissions can globally restrict actions
* **Role-Based Defaults**: Efficient permission management through role assignments

### 1.2 Architecture Integration

The permission system integrates with ProManage's existing architecture:

* **Database Layer**: PostgreSQL with Npgsql connectivity following existing patterns
* **Repository Pattern**: All database access through dedicated repository classes
* **SQL File Organization**: Queries stored in `Modules/Procedures/Permissions/`
* **Service Layer**: Centralized permission resolution and caching services
* **Form Integration**: Seamless integration with existing MDI container and ribbon controls
* **User Management**: Extends existing UserMasterForm and UserManagementListForm

---

## 2. Permission Hierarchy (3-Layer System)

### 2.1 Permission Levels

| Level | Scope | Override Power | Description |
|-------|-------|----------------|-------------|
| **Level 1** | UserManagement Form Permissions | **Highest** | Global restrictions that override all other permissions. If a user is denied Edit access to UserManagement form, editing is disabled across ALL forms. |
| **Level 2** | User-Specific Form Permissions | **Overrides Role** | Per-user, per-form permission overrides. Takes precedence over role permissions but is subject to Level 1 restrictions. |
| **Level 3** | Role-Based Form Permissions | **Default Base** | Default permissions assigned by role. Controls form visibility in ribbon. If Role Read = false, form is hidden regardless of user overrides. |

### 2.2 Permission Resolution Algorithm

```
For each form and permission type:
1. Check Level 1 (UserManagement restrictions):
   - If UserManagement form permission is denied for this permission type → DENY globally
2. Check Level 2 (User-specific overrides):
   - If user-specific permission exists → use that value
3. Check Level 3 (Role-based permissions):
   - Use role permission value
4. Default to DENY if no permissions found

Special case for form visibility:
- Form appears in ribbon ONLY if Role has Read = true
- User overrides cannot make forms visible if role denies Read access
```

---

## 3. Complete Database Schema

### 3.1 New Tables

#### 3.1.1 roles Table
```sql
CREATE TABLE roles (
    role_id SERIAL PRIMARY KEY,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    created_date TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_date TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Indexes
CREATE INDEX idx_roles_name ON roles(role_name);
CREATE INDEX idx_roles_active ON roles(is_active);
```

#### 3.1.2 forms Table
```sql
CREATE TABLE forms (
    form_id SERIAL PRIMARY KEY,
    form_name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    created_date TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Indexes
CREATE INDEX idx_forms_name ON forms(form_name);
CREATE INDEX idx_forms_active ON forms(is_active);
```

#### 3.1.3 permissions Table
```sql
CREATE TABLE permissions (
    permission_id SERIAL PRIMARY KEY,
    permission_name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    sort_order INTEGER DEFAULT 0
);

-- Indexes
CREATE INDEX idx_permissions_name ON permissions(permission_name);
```

#### 3.1.4 role_form_permissions Table (Level 3)
```sql
CREATE TABLE role_form_permissions (
    role_permission_id SERIAL PRIMARY KEY,
    role_id INTEGER NOT NULL REFERENCES roles(role_id) ON DELETE CASCADE,
    form_id INTEGER NOT NULL REFERENCES forms(form_id) ON DELETE CASCADE,
    permission_id INTEGER NOT NULL REFERENCES permissions(permission_id) ON DELETE CASCADE,
    is_granted BOOLEAN NOT NULL,
    created_date TIMESTAMP DEFAULT NOW() NOT NULL,
    UNIQUE(role_id, form_id, permission_id)
);

-- Indexes
CREATE INDEX idx_role_form_permissions_lookup ON role_form_permissions(role_id, form_id, permission_id);
CREATE INDEX idx_role_form_permissions_role ON role_form_permissions(role_id);
```

#### 3.1.5 user_form_permissions Table (Level 2)
```sql
CREATE TABLE user_form_permissions (
    user_permission_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    form_id INTEGER NOT NULL REFERENCES forms(form_id) ON DELETE CASCADE,
    permission_id INTEGER NOT NULL REFERENCES permissions(permission_id) ON DELETE CASCADE,
    is_granted BOOLEAN NOT NULL,
    created_date TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_date TIMESTAMP DEFAULT NOW() NOT NULL,
    UNIQUE(user_id, form_id, permission_id)
);

-- Indexes
CREATE INDEX idx_user_form_permissions_lookup ON user_form_permissions(user_id, form_id, permission_id);
CREATE INDEX idx_user_form_permissions_user ON user_form_permissions(user_id);
```

#### 3.1.6 user_management_permissions Table (Level 1)
```sql
CREATE TABLE user_management_permissions (
    user_mgmt_permission_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    permission_id INTEGER NOT NULL REFERENCES permissions(permission_id) ON DELETE CASCADE,
    is_granted BOOLEAN NOT NULL,
    created_date TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_date TIMESTAMP DEFAULT NOW() NOT NULL,
    UNIQUE(user_id, permission_id)
);

-- Indexes
CREATE INDEX idx_user_management_permissions_lookup ON user_management_permissions(user_id, permission_id);
```

### 3.2 Users Table Modification

#### 3.2.1 Add role_id Foreign Key
```sql
-- Add new role_id column
ALTER TABLE users ADD COLUMN role_id INTEGER;

-- Create foreign key constraint (after data migration)
ALTER TABLE users ADD CONSTRAINT fk_users_role_id
    FOREIGN KEY (role_id) REFERENCES roles(role_id);

-- Create index
CREATE INDEX idx_users_role_id ON users(role_id);

-- Eventually drop old role column (after migration)
-- ALTER TABLE users DROP COLUMN role;
```

---

## 4. Default Data Setup

### 4.1 Default Roles
```sql
INSERT INTO roles (role_name, description) VALUES
('Administrator', 'Full system access with all permissions'),
('Manager', 'Management level access with most permissions'),
('User', 'Standard user with limited permissions'),
('ReadOnly', 'Read-only access to most forms');
```

### 4.2 Default Forms
```sql
INSERT INTO forms (form_name, display_name, description) VALUES
('EstimateForm', 'Estimate Management', 'Create and manage customer estimates'),
('UserMasterForm', 'User Entry', 'Individual user management'),
('UserManagementListForm', 'User Management', 'User list and bulk operations'),
('SQLQueryForm', 'SQL Query Tool', 'Database query execution'),
('DatabaseForm', 'Database Configuration', 'Database connection settings'),
('PermissionManagementForm', 'Permission Management', 'Role and user permission management');
```

### 4.3 Default Permissions
```sql
INSERT INTO permissions (permission_name, description, sort_order) VALUES
('Read', 'View and access forms', 1),
('New', 'Create new records', 2),
('Edit', 'Modify existing records', 3),
('Delete', 'Remove records', 4);
```

### 4.4 Default Administrator Permissions
```sql
-- Grant all permissions to Administrator role for all forms
INSERT INTO role_form_permissions (role_id, form_id, permission_id, is_granted)
SELECT r.role_id, f.form_id, p.permission_id, true
FROM roles r
CROSS JOIN forms f
CROSS JOIN permissions p
WHERE r.role_name = 'Administrator';
```

---

## 5. Migration Strategy

### 5.1 Migration Phases

#### Phase 1: Schema Creation
1. Create new permission tables with indexes and constraints
2. Populate default data (roles, forms, permissions)
3. Create default role-based permissions for Administrator role

#### Phase 2: Data Migration
1. Map existing user.role string values to role_id references:
   - 'Admin' → Administrator role
   - 'Manager' → Manager role
   - 'User' → User role
   - Unknown/NULL → User role (default)
2. Add foreign key constraint to users.role_id
3. Validate data integrity

#### Phase 3: Application Integration
1. Deploy permission service classes
2. Update forms with permission checks
3. Implement permission management UI
4. Test permission resolution logic

#### Phase 4: Cleanup
1. Remove old role column from users table
2. Update documentation
3. Train users on new permission system

### 5.2 Migration SQL Script
```sql
-- Phase 1: Create schema (see section 3 above)

-- Phase 2: Migrate existing data
UPDATE users SET role_id = (
    CASE
        WHEN LOWER(role) = 'admin' THEN (SELECT role_id FROM roles WHERE role_name = 'Administrator')
        WHEN LOWER(role) = 'manager' THEN (SELECT role_id FROM roles WHERE role_name = 'Manager')
        WHEN LOWER(role) = 'user' THEN (SELECT role_id FROM roles WHERE role_name = 'User')
        ELSE (SELECT role_id FROM roles WHERE role_name = 'User')
    END
);

-- Verify migration
SELECT role, role_id, COUNT(*)
FROM users
GROUP BY role, role_id;
```

---

## 6. SQL Query Implementation

### 6.1 File Organization
Following ProManage's SQL organization pattern in `Modules/Procedures/Permissions/`:

- **PermissionCRUD.sql**: Basic CRUD operations for all permission tables
- **PermissionResolution.sql**: Complex permission resolution queries
- **PermissionManagement.sql**: Administrative operations and bulk updates
- **PermissionMigration.sql**: Migration scripts and data setup

### 6.2 Key SQL Queries

#### 6.2.1 GetUserEffectivePermissions
```sql
-- [GetUserEffectivePermissions] --
WITH permission_hierarchy AS (
    -- Level 1: UserManagement restrictions (highest priority)
    SELECT 1 as level, 'UserManagement' as source,
           ump.user_id, 'UserManagementForm' as form_name,
           p.permission_name, ump.is_granted
    FROM user_management_permissions ump
    JOIN permissions p ON ump.permission_id = p.permission_id
    WHERE ump.user_id = @user_id

    UNION ALL

    -- Level 1: Global restrictions for other forms
    SELECT 1 as level, 'GlobalRestriction' as source,
           ump.user_id, f.form_name, p.permission_name,
           CASE WHEN ump.is_granted = false THEN false ELSE null END as is_granted
    FROM user_management_permissions ump
    CROSS JOIN forms f
    JOIN permissions p ON ump.permission_id = p.permission_id
    WHERE ump.user_id = @user_id AND f.form_name != 'UserManagementForm'

    UNION ALL

    -- Level 2: User-specific overrides
    SELECT 2 as level, 'UserOverride' as source,
           ufp.user_id, f.form_name, p.permission_name, ufp.is_granted
    FROM user_form_permissions ufp
    JOIN forms f ON ufp.form_id = f.form_id
    JOIN permissions p ON ufp.permission_id = p.permission_id
    WHERE ufp.user_id = @user_id

    UNION ALL

    -- Level 3: Role-based permissions
    SELECT 3 as level, 'Role' as source,
           u.user_id, f.form_name, p.permission_name, rfp.is_granted
    FROM users u
    JOIN roles r ON u.role_id = r.role_id
    JOIN role_form_permissions rfp ON r.role_id = rfp.role_id
    JOIN forms f ON rfp.form_id = f.form_id
    JOIN permissions p ON rfp.permission_id = p.permission_id
    WHERE u.user_id = @user_id
)
SELECT form_name, permission_name,
       COALESCE(
           (SELECT is_granted FROM permission_hierarchy
            WHERE level = 1 AND is_granted IS NOT NULL
            ORDER BY level LIMIT 1),
           (SELECT is_granted FROM permission_hierarchy
            WHERE level = 2 LIMIT 1),
           (SELECT is_granted FROM permission_hierarchy
            WHERE level = 3 LIMIT 1),
           false
       ) as effective_permission
FROM permission_hierarchy
GROUP BY form_name, permission_name
ORDER BY form_name, permission_name;
-- [End] --
```

#### 6.2.2 HasUserPermission
```sql
-- [HasUserPermission] --
WITH permission_check AS (
    -- Check UserManagement restrictions first
    SELECT CASE
        WHEN EXISTS (
            SELECT 1 FROM user_management_permissions ump
            JOIN permissions p ON ump.permission_id = p.permission_id
            WHERE ump.user_id = @user_id
            AND p.permission_name = @permission_name
            AND ump.is_granted = false
        ) THEN false
        ELSE null
    END as global_restriction

    UNION ALL

    -- Check user-specific override
    SELECT ufp.is_granted
    FROM user_form_permissions ufp
    JOIN forms f ON ufp.form_id = f.form_id
    JOIN permissions p ON ufp.permission_id = p.permission_id
    WHERE ufp.user_id = @user_id
    AND f.form_name = @form_name
    AND p.permission_name = @permission_name

    UNION ALL

    -- Check role-based permission
    SELECT rfp.is_granted
    FROM users u
    JOIN roles r ON u.role_id = r.role_id
    JOIN role_form_permissions rfp ON r.role_id = rfp.role_id
    JOIN forms f ON rfp.form_id = f.form_id
    JOIN permissions p ON rfp.permission_id = p.permission_id
    WHERE u.user_id = @user_id
    AND f.form_name = @form_name
    AND p.permission_name = @permission_name
)
SELECT COALESCE(
    (SELECT global_restriction FROM permission_check WHERE global_restriction IS NOT NULL),
    (SELECT is_granted FROM permission_check WHERE is_granted IS NOT NULL ORDER BY CASE WHEN global_restriction IS NULL THEN 1 ELSE 2 END LIMIT 1),
    false
) as has_permission;
-- [End] --
```

#### 6.2.3 GetUserVisibleForms
```sql
-- [GetUserVisibleForms] --
SELECT DISTINCT f.form_name, f.display_name
FROM forms f
JOIN role_form_permissions rfp ON f.form_id = rfp.form_id
JOIN permissions p ON rfp.permission_id = p.permission_id
JOIN users u ON rfp.role_id = u.role_id
WHERE u.user_id = @user_id
AND p.permission_name = 'Read'
AND rfp.is_granted = true
AND f.is_active = true
ORDER BY f.display_name;
-- [End] --
```

---

## 7. Application Architecture

### 7.1 File Structure
```
Modules/
├── Data/Permissions/
│   ├── PermissionRepository.cs
│   └── PermissionManagementRepository.cs
├── Models/Permissions/
│   ├── RoleModel.cs
│   ├── FormModel.cs
│   ├── PermissionModel.cs
│   ├── UserPermissionModel.cs
│   └── EffectivePermissionModel.cs
├── Services/
│   ├── PermissionService.cs
│   └── PermissionCacheService.cs
├── Helpers/Permissions/
│   ├── PermissionHelper.cs
│   └── PermissionValidation.cs
└── Procedures/Permissions/
    ├── PermissionCRUD.sql
    ├── PermissionResolution.sql
    ├── PermissionManagement.sql
    └── PermissionMigration.sql

Forms/
├── PermissionManagementForm.cs
├── PermissionManagementForm.Designer.cs
└── PermissionManagementForm.resx
```

### 7.2 Core Service Classes

#### 7.2.1 PermissionService
```csharp
public class PermissionService
{
    // Core permission checking methods
    public bool HasPermission(int userId, string formName, string permissionType);
    public Dictionary<string, Dictionary<string, bool>> GetUserPermissions(int userId);
    public List<string> GetVisibleForms(int userId);
    public void RefreshUserPermissions(int userId);

    // Permission management methods
    public bool SetRolePermission(int roleId, string formName, string permissionType, bool isGranted);
    public bool SetUserPermission(int userId, string formName, string permissionType, bool isGranted);
    public bool SetUserManagementPermission(int userId, string permissionType, bool isGranted);
}
```

#### 7.2.2 PermissionCacheService
```csharp
public class PermissionCacheService
{
    // Cache management for performance
    public void LoadUserPermissions(int userId);
    public void ClearUserCache(int userId);
    public void RefreshAllCaches();

    // Fast permission lookups
    public bool GetCachedPermission(int userId, string formName, string permissionType);
}
```

---

## 8. User Interface Implementation

### 8.1 PermissionManagementForm Design

#### Tab 1: Role Permissions
- **Grid Layout**: Roles as rows, Forms as column groups, Permissions as sub-columns
- **Checkboxes**: Read/New/Edit/Delete permissions for each role-form combination
- **Bulk Operations**: Select all/none for roles or forms
- **Save Changes**: Batch update to role_form_permissions table

#### Tab 2: User Overrides
- **User Selection**: Dropdown or searchable grid for user selection
- **Permission Grid**: Similar to role permissions but for individual users
- **Inheritance Display**: Show which permissions are inherited from role vs overridden
- **Conditional Display**: Only show forms where role has Read=true

#### Tab 3: UserManagement Restrictions
- **User Selection**: Dropdown or searchable grid for user selection
- **Simple Checkboxes**: New/Edit/Delete restrictions for UserManagement form
- **Global Impact Warning**: Clear indication that these restrictions apply globally
- **Audit Trail**: Show when restrictions were applied and by whom

### 8.2 Integration with Existing Forms

#### 8.2.1 UserMasterForm Enhancement
```csharp
// Add new tab to existing tab control
private void AddPermissionsTab()
{
    var permissionsTab = new DevExpress.XtraTab.XtraTabPage();
    permissionsTab.Text = "Permissions";
    permissionsTab.Name = "tabPermissions";

    // Add read-only permission display grid
    var permissionGrid = new DevExpress.XtraGrid.GridControl();
    // Configure grid to show user's effective permissions

    tabControl.TabPages.Add(permissionsTab);
}

// Permission checking in form events
private void UserMasterForm_Load(object sender, EventArgs e)
{
    if (!PermissionService.HasPermission(CurrentUser.UserId, "UserMasterForm", "Read"))
    {
        MessageBox.Show("Access denied.", "Permission Error");
        this.Close();
        return;
    }

    // Enable/disable buttons based on permissions
    btnSave.Enabled = PermissionService.HasPermission(CurrentUser.UserId, "UserMasterForm", "Edit");
    btnNew.Enabled = PermissionService.HasPermission(CurrentUser.UserId, "UserMasterForm", "New");
    btnDelete.Enabled = PermissionService.HasPermission(CurrentUser.UserId, "UserMasterForm", "Delete");
}
```

#### 8.2.2 MainFrame Ribbon Filtering
```csharp
private void FilterRibbonByPermissions()
{
    var visibleForms = PermissionService.GetVisibleForms(CurrentUser.UserId);

    foreach (DevExpress.XtraBars.BarButtonItem item in ribbon.Items)
    {
        if (item.Tag is string formName)
        {
            item.Visibility = visibleForms.Contains(formName)
                ? DevExpress.XtraBars.BarItemVisibility.Always
                : DevExpress.XtraBars.BarItemVisibility.Never;
        }
    }
}
```

---

## 9. Performance Considerations

### 9.1 Caching Strategy
- **User Permission Cache**: Load all user permissions into memory after login
- **Cache Invalidation**: Refresh cache when permissions change
- **Memory Efficiency**: Use efficient data structures for O(1) permission lookups
- **Cache Expiration**: Automatic cache refresh every 30 minutes

### 9.2 Database Optimization
- **Proper Indexing**: Composite indexes on frequently queried columns
- **Query Optimization**: Efficient SQL queries with minimal joins
- **Connection Pooling**: Reuse database connections for permission checks
- **Batch Operations**: Bulk updates for permission changes

### 9.3 Performance Metrics
- **Permission Check Time**: Target <1ms for cached permissions
- **Cache Load Time**: Target <100ms for full user permission load
- **Database Query Time**: Target <50ms for complex permission resolution
- **Memory Usage**: Target <1MB per user for permission cache

---

## 10. Testing Strategy

### 10.1 Unit Testing
- **Permission Resolution Logic**: Test all three permission levels
- **Cache Performance**: Verify cache hit/miss ratios
- **SQL Query Validation**: Test complex permission queries
- **Edge Cases**: Test with missing permissions, invalid users

### 10.2 Integration Testing
- **Form Integration**: Test permission checks in all forms
- **Ribbon Filtering**: Verify correct form visibility
- **User Management**: Test permission management UI
- **Migration Testing**: Validate data migration scripts

### 10.3 Performance Testing
- **Load Testing**: Test with 1000+ users and permissions
- **Concurrent Access**: Test multiple users accessing permissions
- **Cache Performance**: Measure cache efficiency under load
- **Database Performance**: Test query performance with large datasets

---

## 11. Implementation Checklist

### 11.1 Database Implementation
- [ ] Create permission tables with proper indexes
- [ ] Populate default data (roles, forms, permissions)
- [ ] Create migration scripts for existing users
- [ ] Test migration with sample data
- [ ] Validate foreign key constraints

### 11.2 Application Implementation
- [ ] Create PermissionRepository classes
- [ ] Implement PermissionService with caching
- [ ] Create permission models and DTOs
- [ ] Add SQL queries to Procedures/Permissions/
- [ ] Update SQLQueries.cs with new constants

### 11.3 UI Implementation
- [ ] Create PermissionManagementForm
- [ ] Add permissions tab to UserMasterForm
- [ ] Update UserManagementListForm with permissions button
- [ ] Implement ribbon filtering in MainFrame
- [ ] Add permission checks to all existing forms

### 11.4 Testing and Validation
- [ ] Unit test permission resolution logic
- [ ] Integration test form permission checks
- [ ] Performance test with large datasets
- [ ] User acceptance testing
- [ ] Documentation updates

---

## 12. Behavior Examples

### 12.1 Permission Resolution Examples

| Scenario | Role Read | User Override Edit | UM Form Edit | Final Result |
|----------|-----------|-------------------|--------------|--------------|
| Standard User | TRUE | NULL | TRUE | ✅ Edit allowed (inherits from role) |
| Restricted User | TRUE | TRUE | FALSE | ❌ Edit blocked (UserManagement restriction) |
| Hidden Form | FALSE | TRUE | TRUE | ❌ Form not visible (role blocks visibility) |
| User Override | TRUE | FALSE | TRUE | ❌ Edit blocked (user override) |
| Admin User | TRUE | TRUE | TRUE | ✅ Full access |

### 12.2 Form Visibility Rules
- Form appears in ribbon **ONLY** if Role has Read = true
- User overrides **CANNOT** make forms visible if role denies Read
- UserManagement restrictions **DO NOT** affect form visibility
- Inactive forms are never visible regardless of permissions

### 12.3 Global Restriction Examples
- If user has UserManagement Edit = false → **NO** editing in any form
- If user has UserManagement New = false → **NO** creating records in any form
- If user has UserManagement Delete = false → **NO** deleting records in any form
- UserManagement Read restrictions do not affect other forms

---

## 13. Conclusion

This comprehensive specification provides a complete, implementable role-based access control system for ProManage that:

- **Integrates Seamlessly**: Works with existing ProManage architecture and patterns
- **Provides Flexibility**: Three-layer hierarchy allows granular control
- **Ensures Performance**: Caching and optimized queries for fast permission checks
- **Maintains Security**: Global restrictions prevent privilege escalation
- **Supports Growth**: Scalable design handles hundreds of users and permissions
- **Follows Standards**: Consistent with ProManage naming conventions and file organization

The system is designed to be implemented in phases, allowing for gradual rollout and testing while maintaining backward compatibility with the existing user management system.

> **Next Steps**: Begin with Phase 1 (Schema Creation) and proceed through the implementation checklist systematically. Ensure thorough testing at each phase before proceeding to the next.


